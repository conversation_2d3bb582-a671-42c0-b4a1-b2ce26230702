//! 路由处理器实现
//!
//! 核心路由执行逻辑，支持多种路由模式

use anchor_lang::prelude::*;
use crate::error::RouteError;
use crate::instructions::{ExecuteBatchRoutesAccounts, ExecuteBranchRouteAccounts};
use crate::routing::batched::BatchedRouteExecutor;
use crate::routing::types::{
    RouteConfig, RoutingMode, FlashLoanRouteConfig, BranchRouteConfig, BatchRouteConfig,
    BranchRouteResult, BatchRouteResult
};
use crate::state::{RouterConfig, UserPosition};

/// 主要路由处理器
pub struct RouteProcessor;

impl RouteProcessor {
    /// 验证路由可行性
    pub fn validate_route_feasibility(route_config: &RouteConfig) -> Result<()> {
        // 验证路由配置
        route_config.validate()?;

        // 检查复杂度
        if route_config.complexity_score() > 24 {
            return Err(RouteError::RouteComplexityTooHigh.into());
        }

        Ok(())
    }

    /// 执行通用路由
    pub fn execute_route<'info>(
        accounts: &crate::instructions::execute_route::ExecuteRouteAccounts<'info>,
        route_config: RouteConfig,
        remaining_accounts: &'info [AccountInfo<'info>],
        order_id: u64,
    ) -> Result<u64> {
        msg!("开始执行路由 - 模式: {:?}, 订单ID: {}", route_config.mode, order_id);

        // 1. 验证路由配置
        route_config.validate()?;

        // 2. 安全检查
        Self::perform_security_checks(&accounts.config, &accounts.user_position, &route_config, &accounts.user)?;

        // 3. 根据路由模式执行
        let result = match route_config.mode {
            RoutingMode::Linear => {
                crate::routing::linear::LinearRouteExecutor::execute(
                    &route_config.routes,
                    route_config.amount_in,
                    remaining_accounts,
                    None, // 普通线性路由不使用PDA
                )
            }
            RoutingMode::Circular => {
                crate::routing::circular::CircularRouteExecutor::execute(
                    &route_config.routes,
                    route_config.amount_in,
                    remaining_accounts,
                    route_config.flash_loan.as_ref(),
                    None, // 普通循环路由不使用PDA
                )
            }
            RoutingMode::Branching => {
                // 分支路由需要特殊的配置类型
                Err(RouteError::InvalidRoutingMode.into())
            }
            RoutingMode::Batched => {
                // 批量路由需要特殊的配置类型
                Err(RouteError::InvalidRoutingMode.into())
            }
        }?;

        // 4. 记录执行结果
        Self::log_execution_result(order_id, &route_config, result)?;

        msg!("路由执行完成 - 订单ID: {}, 结果: {}", order_id, result);
        Ok(result)
    }

    /// 执行闪电贷路由
    pub fn execute_flash_loan_route<'info>(
        accounts: &crate::instructions::flash_loan::FlashLoanRouteAccounts<'info>,
        flash_config: FlashLoanRouteConfig,
        remaining_accounts: &'info [AccountInfo<'info>],
        order_id: u64,
    ) -> Result<u64> {
        msg!("开始执行闪电贷路由 - 订单ID: {}", order_id);

        // 1. 验证闪电贷配置
        Self::validate_flash_loan_config(&flash_config)?;

        // 2. 安全检查
        Self::perform_flash_loan_security_checks(&accounts.config, &accounts.user_position, &flash_config)?;

        // 3. 执行闪电贷套利
        let result = crate::routing::circular::CircularRouteExecutor::execute(
            &flash_config.arbitrage_routes,
            flash_config.flash_loan.amount,
            remaining_accounts,
            Some(&flash_config.flash_loan),
            None, // 闪电贷路由不使用PDA
        )?;

        // 4. 验证利润
        if result < flash_config.expected_profit {
            msg!("警告: 实际利润 {} 低于预期 {}", result, flash_config.expected_profit);
        }

        msg!("闪电贷路由执行完成 - 订单ID: {}, 利润: {}", order_id, result);
        Ok(result)
    }

    /// 执行安全检查（使用新的统一安全验证系统）
    fn perform_security_checks(
        config: &Account<RouterConfig>,
        user_position: &Account<UserPosition>,
        route_config: &RouteConfig,
        user: &AccountInfo,
    ) -> Result<()> {
        // 使用新的统一安全验证系统
        crate::utils::validation::comprehensive_route_security_check(
            config,
            user_position,
            route_config,
            user,
        )?;

        msg!("路由安全检查完成 - 用户: {}, 路由模式: {:?}",
            user.key(), route_config.mode);

        Ok(())
    }

    /// 执行闪电贷安全检查（使用新的统一安全验证系统）
    fn perform_flash_loan_security_checks(
        config: &Account<RouterConfig>,
        user_position: &Account<UserPosition>,
        flash_config: &FlashLoanRouteConfig,
    ) -> Result<()> {
        // 使用新的统一闪电贷安全验证系统
        crate::utils::validation::validate_flash_loan_security(
            config,
            user_position,
            flash_config.flash_loan.amount,
            flash_config.expected_profit,
            &flash_config.arbitrage_routes,
        )?;

        // 额外检查Gas费用
        if flash_config.max_gas_fee > config.max_gas_fee {
            msg!("Gas费用过高: {} > {}", flash_config.max_gas_fee, config.max_gas_fee);
            return Err(RouteError::AmountValidationFailed.into());
        }

        msg!("闪电贷安全检查完成 - 金额: {}, 预期利润: {}",
            flash_config.flash_loan.amount, flash_config.expected_profit);

        Ok(())
    }

    /// 验证闪电贷配置
    fn validate_flash_loan_config(config: &FlashLoanRouteConfig) -> Result<()> {
        // 1. 检查套利路径
        if config.arbitrage_routes.is_empty() {
            return Err(RouteError::EmptyRoutePath.into());
        }

        if config.arbitrage_routes.len() > 6 {
            return Err(RouteError::RoutePathTooLong.into());
        }

        // 2. 验证循环路径
        let start_mint = config.arbitrage_routes[0].input_mint;
        let end_mint = config.arbitrage_routes.last().unwrap().output_mint;
        if start_mint != end_mint {
            return Err(RouteError::NotCircularRoute.into());
        }

        // 3. 验证路径连续性
        for i in 0..config.arbitrage_routes.len() - 1 {
            if config.arbitrage_routes[i].output_mint != config.arbitrage_routes[i + 1].input_mint {
                return Err(RouteError::RouteDiscontinuity.into());
            }
        }

        // 4. 检查闪电贷金额合理性
        if config.flash_loan.amount == 0 {
            return Err(RouteError::FlashLoanAmountExceeded.into());
        }

        Ok(())
    }

    /// 记录执行结果
    fn log_execution_result(
        order_id: u64,
        route_config: &RouteConfig,
        result: u64,
    ) -> Result<()> {
        let complexity_score = route_config.complexity_score();
        let steps = route_config.routes.len();

        msg!(
            "路由执行摘要 - 订单: {}, 模式: {:?}, 步骤: {}, 复杂度: {}, 结果: {}",
            order_id,
            route_config.mode,
            steps,
            complexity_score,
            result
        );

        // TODO: 发出事件用于链下监控

        Ok(())
    }

    /// 计算预期Gas消耗
    pub fn estimate_gas_consumption(route_config: &RouteConfig) -> u64 {
        match route_config.mode {
            RoutingMode::Linear => {
                crate::routing::linear::LinearRouteExecutor::estimate_gas_cost(&route_config.routes)
            }
            RoutingMode::Circular => {
                crate::routing::circular::CircularRouteExecutor::estimate_gas_cost(&route_config.routes)
            }
            RoutingMode::Branching => {
                // TODO: 实现分支路由Gas估算
                100_000
            }
            RoutingMode::Batched => {
                // TODO: 实现批量路由Gas估算
                150_000
            }
        }
    }


    /// 执行分支路由
    pub fn execute_branch_route<'info>(
        accounts: &ExecuteBranchRouteAccounts<'info>,
        branch_config: BranchRouteConfig,
        remaining_accounts: &'info [AccountInfo<'info>],
        order_id: u64,
    ) -> Result<BranchRouteResult> {
        msg!("开始执行分支路由 - 订单ID: {}", order_id);

        // 1. 验证分支路由配置
        crate::routing::branching::BranchingRouteExecutor::validate_config(&branch_config)?;

        // 2. 安全检查
        Self::perform_branch_security_checks(&accounts.config, &accounts.user_position, &branch_config)?;

        // 3. 执行分支路由
        let total_amount_in: u64 = branch_config.input_distribution.iter()
            .map(|&dist| dist as u64)
            .sum::<u64>() * 100; // 转换基点为实际数量

        let result = crate::routing::branching::BranchingRouteExecutor::execute_detailed(
            &branch_config,
            total_amount_in,
            remaining_accounts,
            None,
        )?;

        msg!("分支路由执行完成 - 订单ID: {}, 总输出: {}", order_id, result.total_output);
        Ok(result)
    }

    /// 执行批量路由
    pub fn execute_batch_routes<'info>(
        accounts: &ExecuteBatchRoutesAccounts<'info>,
        batch_config: BatchRouteConfig,
        remaining_accounts: &'info [AccountInfo<'info>],
        order_id: u64,
    ) -> Result<BatchRouteResult> {
        msg!("开始执行批量路由 - 订单ID: {}", order_id);

        // 1. 验证批量路由配置
        BatchedRouteExecutor::validate_config(&batch_config)?;

        // 2. 安全检查
        Self::perform_batch_security_checks(&accounts.config, &accounts.user_position, &batch_config)?;

        // 3. 执行批量路由
        let result = BatchedRouteExecutor::execute(
            &batch_config,
            remaining_accounts,
            None,
        )?;

        msg!("批量路由执行完成 - 订单ID: {}, 成功: {:?}/{:?}",
            order_id, result, result);
        Ok(result)
    }

    /// 估算分支路由Gas消耗
    pub fn estimate_branch_gas_consumption(branch_config: &BranchRouteConfig) -> u64 {
        crate::routing::branching::BranchingRouteExecutor::estimate_gas_cost(branch_config)
    }

    /// 估算批量路由Gas消耗
    pub fn estimate_batch_gas_consumption(batch_config: &BatchRouteConfig) -> u64 {
        crate::routing::batched::BatchedRouteExecutor::estimate_gas_cost(batch_config)
    }

    /// 执行分支路由安全检查
    fn perform_branch_security_checks(
        config: &Account<RouterConfig>,
        user_position: &Account<UserPosition>,
        branch_config: &BranchRouteConfig,
    ) -> Result<()> {
        // 1. 全局紧急停止检查
        if config.emergency_stop {
            return Err(RouteError::GlobalEmergencyStop.into());
        }

        // 2. 用户状态检查
        if user_position.is_suspended {
            return Err(RouteError::UserSuspended.into());
        }

        // 3. 检查分支数量限制
        if branch_config.branch_routes.len() > 6 {
            return Err(RouteError::RoutePathTooLong.into());
        }

        // 4. 检查各分支中的DEX紧急停止
        for branch in &branch_config.branch_routes {
            for route in branch {
                if config.dex_emergency_stops.iter().any(|&dex| dex == route.dex) {
                    return Err(RouteError::DexEmergencyStop.into());
                }
            }
        }

        Ok(())
    }

    /// 执行批量路由安全检查
    fn perform_batch_security_checks(
        config: &Account<RouterConfig>,
        user_position: &Account<UserPosition>,
        batch_config: &BatchRouteConfig,
    ) -> Result<()> {
        // 1. 全局紧急停止检查
        if config.emergency_stop {
            return Err(RouteError::GlobalEmergencyStop.into());
        }

        // 2. 用户状态检查
        if user_position.is_suspended {
            return Err(RouteError::UserSuspended.into());
        }

        // 3. 检查批量数量限制
        if batch_config.routes.len() > 8 {
            return Err(RouteError::RoutePathTooLong.into());
        }

        // 4. 检查各路由配置
        for route_config in &batch_config.routes {
            // 检查金额限制
            if route_config.amount_in > config.max_route_amount {
                return Err(RouteError::AmountValidationFailed.into());
            }

            // 检查滑点限制
            if route_config.max_slippage_bps > config.max_slippage_bps {
                return Err(RouteError::SlippageTooHigh.into());
            }

            // 检查DEX紧急停止
            for route in &route_config.routes {
                if config.dex_emergency_stops.iter().any(|&dex| dex == route.dex) {
                    return Err(RouteError::DexEmergencyStop.into());
                }
            }
        }

        Ok(())
    }
}
