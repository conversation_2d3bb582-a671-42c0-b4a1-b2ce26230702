//! 路由执行指令
//! 
//! 处理通用多跳路由的执行，集成统一的错误处理和事件系统

use anchor_lang::prelude::*;
use anchor_spl::token::{Token, TokenAccount};
use anchor_spl::token_interface::{Mint, TokenInterface};
use crate::state::{RouterConfig, UserPosition};
use crate::routing::types::{RouteConfig, Dex};
use crate::processor::RouteProcessor;
use crate::error::{RouteError};
use crate::utils::{
    log_arbitrage_operation, LogLevel, LogContext, OperationType,
    log_route_execution_error, log_performance_metrics, SmartRetryWrapper, RetryStrategy
};
use crate::state::event::{
    RouteExecuted, RouteFailed,
    SecurityAlert, SecurityAlertType, SecuritySeverity
};

/// 执行路由的账户结构
#[derive(Accounts)]
pub struct ExecuteRouteAccounts<'info> {
    #[account(mut)]
    pub user: Signer<'info>,
    
    #[account(
        seeds = [b"config"],
        bump,
        constraint = !config.emergency_stop @ crate::error::RouteError::GlobalEmergencyStop
    )]
    pub config: Account<'info, RouterConfig>,
    
    #[account(
        mut,
        seeds = [b"position", user.key().as_ref()],
        bump,
        constraint = !user_position.is_suspended @ crate::error::RouteError::UserSuspended
    )]
    pub user_position: Account<'info, UserPosition>,
    
    /// 源代币账户
    #[account(
        mut,
        token::authority = user,
        constraint = source_token_account.amount > 0 @ crate::error::RouteError::InsufficientBalance
    )]
    pub source_token_account: Account<'info, TokenAccount>,
    
    /// 目标代币账户
    #[account(
        mut,
        token::authority = user
    )]
    pub destination_token_account: Account<'info, TokenAccount>,
    
    /// 源代币mint
    pub source_mint: InterfaceAccount<'info, Mint>,
    
    /// 目标代币mint
    pub destination_mint: InterfaceAccount<'info, Mint>,
    
    /// Token程序
    pub token_program: Program<'info, Token>,
    
    /// Token2022程序（可选，用于新版Token）
    pub token_2022_program: Option<Interface<'info, TokenInterface>>,
    
    /// 系统程序
    pub system_program: Program<'info, System>,
}

/// 执行路由处理器
pub fn execute_route_handler<'a>(
    ctx: Context<'_, '_, 'a, 'a, ExecuteRouteAccounts<'a>>,
    route_config: RouteConfig,
    order_id: u64,
) -> Result<()> {
    let user_key = ctx.accounts.user.key();
    let start_time = Clock::get()?.unix_timestamp;
    let start_time_ms = (start_time * 1000) as u64;
    
    // 记录路由执行开始
    log_arbitrage_operation(
        LogLevel::Info,
        "Route execution started",
        &LogContext::new(OperationType::RouteExecution)
            .with_user(user_key)
            .with_order_id(order_id)
            .with_amount(route_config.amount_in)
            .with_data(format!("Mode: {:?}, Steps: {}", route_config.mode, route_config.routes.len())),
    );

    // 执行路由并处理错误恢复
    let execution_result = execute_route_with_recovery(
        &ctx,
        &route_config,
        order_id,
        start_time_ms,
    );

    match execution_result {
        Ok(result) => {
            let execution_time_ms = ((Clock::get()?.unix_timestamp * 1000) as u64) - start_time_ms;
            
            // 更新用户统计
            let user_position = &mut ctx.accounts.user_position;
            user_position.total_volume += route_config.amount_in;
            user_position.successful_routes += 1;
            user_position.last_activity = start_time;
            
            // 计算实际滑点
            let expected_out = route_config.min_amount_out;
            let actual_slippage_bps = if expected_out > 0 {
                ((expected_out.saturating_sub(result)) * 10000 / expected_out).min(10000) as u16
            } else {
                0
            };

            // 处理套利场景
            let mut profit = 0u64;
            if let crate::routing::RoutingMode::Circular = route_config.mode {
                if result > route_config.amount_in {
                    profit = result - route_config.amount_in;
                    user_position.total_profit += profit;
                    
                    log_arbitrage_operation(
                        LogLevel::Info,
                        "Arbitrage profit achieved",
                        &LogContext::new(OperationType::RouteExecution)
                            .with_user(user_key)
                            .with_order_id(order_id)
                            .with_amount(profit),
                    );
                }
            }

            // 发射成功执行事件
            RouteExecuted::emit_success(
                user_key,
                order_id,
                route_config.mode,
                route_config.amount_in,
                result,
                route_config.routes.len() as u8,
                extract_dex_path(&route_config),
                execution_time_ms,
                0, // gas_used - 需要从执行上下文获取
                actual_slippage_bps,
            );

            // 记录性能指标
            log_performance_metrics(
                "route_execution",
                execution_time_ms,
                0, // gas_used
                Some(&user_key),
                Some(order_id),
            );

            log_arbitrage_operation(
                LogLevel::Info,
                "Route execution completed successfully",
                &LogContext::new(OperationType::RouteExecution)
                    .with_user(user_key)
                    .with_order_id(order_id)
                    .with_amount(result)
                    .with_data(format!("Profit: {}, Time: {}ms", profit, execution_time_ms)),
            );

            Ok(())
        },
        Err(error) => {
            // 更新失败统计
            let user_position = &mut ctx.accounts.user_position;
            user_position.failed_routes += 1;
            user_position.last_activity = start_time;

            // 记录详细错误信息
            log_route_execution_error(
                &error,
                "Route execution failed",
                Some(&user_key),
                Some(order_id),
                None, // failed_step - 需要从错误上下文获取
                None, // failed_dex
            );

            // 发射失败事件
            RouteFailed::emit_failure(
                user_key,
                order_id,
                error as u32,
                0, // failed_step - 需要改进错误处理以提供更多上下文
                Dex::RaydiumClmm, // failed_dex - 需要从执行上下文获取
                route_config.amount_in,
            );

            // 检查是否需要安全警报
            check_security_alerts(&error, &user_key, order_id);

            Err(error.into())
        }
    }
}

/// 带错误恢复的路由执行
fn execute_route_with_recovery<'a>(
    ctx: &Context<'_, '_, 'a, 'a, ExecuteRouteAccounts<'a>>,
    route_config: &RouteConfig,
    order_id: u64,
    start_time_ms: u64,
) -> Result<u64, RouteError> {
    // 1. 验证路由可行性
    RouteProcessor::validate_route_feasibility(route_config)
        .map_err(|e| {
            log_route_execution_error(
                &RouteError::InvalidRouteConfig,
                "Route feasibility validation failed",
                Some(&ctx.accounts.user.key()),
                Some(order_id),
                None,
                None,
            );
            RouteError::InvalidRouteConfig
        })?;

    // 2. 验证输入代币和数量
    let source_balance = ctx.accounts.source_token_account.amount;
    if source_balance < route_config.amount_in {
        let error = RouteError::InsufficientBalance;
        log_route_execution_error(
            &error,
            &format!("Insufficient balance: {} < {}", source_balance, route_config.amount_in),
            Some(&ctx.accounts.user.key()),
            Some(order_id),
            None,
            None,
        );
        return Err(Error::from(error));
    }

    // 3. 验证路由配置中的代币mint与账户匹配
    if !route_config.routes.is_empty() {
        let first_route = &route_config.routes[0];
        let last_route = &route_config.routes[route_config.routes.len() - 1];
        
        if first_route.input_mint != ctx.accounts.source_mint.key() ||
           last_route.output_mint != ctx.accounts.destination_mint.key() {
            let error = RouteError::InvalidRouteConfig;
            log_route_execution_error(
                &error,
                "Token mint mismatch in route configuration",
                Some(&ctx.accounts.user.key()),
                Some(order_id),
                None,
                None,
            );
            return Err(Error::from(error));
        }
    }

    // 4. 执行路由（使用智能重试机制）
    let retry_strategy = RetryStrategy::default();
    let execution_operation = || {
        RouteProcessor::execute_route(
            ctx.accounts,
            route_config.clone(),
            ctx.remaining_accounts,
            order_id,
        ).map_err(|e| RouteError::DexOperationFailed)
    };

    let smart_retry = SmartRetryWrapper::new(execution_operation, retry_strategy);
    let result = smart_retry.execute()?;

    // 5. 验证最小输出
    if result < route_config.min_amount_out {
        let error = RouteError::SlippageTooHigh;
        log_route_execution_error(
            &error,
            &format!("Output below minimum: {} < {}", result, route_config.min_amount_out),
            Some(&ctx.accounts.user.key()),
            Some(order_id),
            None,
            None,
        );
        return Err(error);
    }

    Ok(result)
}

/// 从路由配置中提取DEX路径
fn extract_dex_path(route_config: &RouteConfig) -> Vec<Dex> {
    route_config.routes
        .iter()
        .map(|route| route.dex)
        .collect()
}

/// 检查是否需要发送安全警报
fn check_security_alerts(error: &RouteError, user: &Pubkey, order_id: u64) {
    match error {
        RouteError::ReentrancyDetected => {
            SecurityAlert::emit_alert(
                SecurityAlertType::ReentrancyAttempt,
                SecuritySeverity::Critical,
                Some(*user),
                "Reentrancy attack detected".to_string(),
                format!("Order ID: {}", order_id),
            );
        },
        RouteError::UserSuspended => {
            SecurityAlert::emit_alert(
                SecurityAlertType::UnauthorizedAccess,
                SecuritySeverity::High,
                Some(*user),
                "Suspended user attempted operation".to_string(),
                format!("Order ID: {}", order_id),
            );
        },
        RouteError::RiskScoreTooHigh => {
            SecurityAlert::emit_alert(
                SecurityAlertType::SuspiciousPattern,
                SecuritySeverity::Medium,
                Some(*user),
                "High risk score detected".to_string(),
                format!("Order ID: {}", order_id),
            );
        },
        _ => {
            // 对于其他错误，检查是否符合可疑模式
            // 这里可以添加更复杂的模式检测逻辑
        }
    }
}